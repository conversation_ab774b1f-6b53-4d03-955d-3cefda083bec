// Test script to verify message button functionality
// This simulates the flow when the message button is pressed

const { matchesMessagesIntegration } = require('./services/matchesMessagesIntegration');

async function testMessageButton() {
  console.log('🧪 Testing message button functionality...');
  
  try {
    // Test with <PERSON>'s user ID (the one shown in the image)
    const userId = '2'; // Sophia from likesService.ts
    
    console.log('🧪 Testing conversation creation for user:', userId);
    
    // This simulates what happens when the message button is pressed
    const conversationId = await matchesMessagesIntegration.createConversationForLike(userId);
    
    console.log('✅ Conversation created successfully with ID:', conversationId);
    console.log('✅ Message button functionality is working!');
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Run the test
testMessageButton().then(success => {
  if (success) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('💥 Tests failed!');
  }
});
