import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import ChatScreen from '@/components/messaging/ChatScreen';
import { Message, User, Conversation } from '@/types/messaging';
import { useMessagesStore } from '@/stores/messagesStore';

// Mock data - replace with real data from your backend
const CURRENT_USER: User = {
  id: 'current-user',
  name: 'You',
  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  isOnline: true,
};

const MOCK_CONVERSATION: Conversation = {
  id: 'conv-1',
  participants: [
    CURRENT_USER,
    {
      id: 'user-1',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      isOnline: true,
    }
  ],
  lastMessage: {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! Thanks for the like 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
  unreadCount: 0,
  isTyping: false,
  typingUsers: [],
};

const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! How are you doing?',
    type: 'text',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-2',
    senderId: 'current-user',
    receiverId: 'user-1',
    content: 'I\'m doing great! Thanks for asking 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-3',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'That\'s awesome! Want to grab coffee this weekend?',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
];

export default function ChatPage() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);

  const {
    conversations,
    currentUser,
    getMessagesForConversation,
    sendMessage,
    markAsRead,
    findConversationByParticipant,
    fetchConversations,
  } = useMessagesStore();

  useEffect(() => {
    const loadConversation = async () => {
      if (typeof id === 'string') {
        try {
          // Ensure conversations are loaded
          if (conversations.length === 0) {
            await fetchConversations();
          }

          // Find conversation by participant ID
          const foundConversation = findConversationByParticipant(id);

          if (foundConversation) {
            setConversation(foundConversation);

            // Load messages for this conversation
            const conversationMessages = getMessagesForConversation(foundConversation.id);
            setMessages(conversationMessages);
          } else {
            // Use mock data as fallback
            setConversation(MOCK_CONVERSATION);
            setMessages(MOCK_MESSAGES);
          }
        } catch (error) {
          console.error('Error loading conversation:', error);
          // Use mock data as fallback
          setConversation(MOCK_CONVERSATION);
          setMessages(MOCK_MESSAGES);
        }
      }
    };

    loadConversation();
  }, [id, conversations]);

  const handleSendMessage = async (content: string, type: 'text' | 'image' | 'file') => {
    if (!conversation || !currentUser) return;

    try {
      await sendMessage(conversation.id, content, type);

      // Refresh messages
      const updatedMessages = getMessagesForConversation(conversation.id);
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleStartCall = (type: 'audio' | 'video') => {
    // TODO: Implement call functionality
    console.log('Starting call:', type);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleStartTyping = () => {
    // TODO: Implement typing indicators
  };

  const handleStopTyping = () => {
    // TODO: Implement typing indicators
  };

  const handleMarkAsRead = (messageId: string) => {
    if (conversation) {
      markAsRead(conversation.id, messageId);
    }
  };

  if (!conversation) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Loading conversation...</Text>
      </View>
    );
  }

  if (!currentUser) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Setting up user...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ChatScreen
        conversation={conversation}
        messages={messages}
        currentUser={currentUser}
        onSendMessage={handleSendMessage}
        onStartCall={handleStartCall}
        onGoBack={handleGoBack}
        onStartTyping={handleStartTyping}
        onStopTyping={handleStopTyping}
        onMarkAsRead={handleMarkAsRead}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});