import { useMessagesStore } from '@/stores/messagesStore';
import { useLikesStore } from '@/stores/likesStore';
import { Match } from '@/types/messaging';

export class MatchesMessagesIntegration {
  private static instance: MatchesMessagesIntegration;

  public static getInstance(): MatchesMessagesIntegration {
    if (!MatchesMessagesIntegration.instance) {
      MatchesMessagesIntegration.instance = new MatchesMessagesIntegration();
    }
    return MatchesMessagesIntegration.instance;
  }

  /**
   * Sync match data with conversation data
   * This ensures matches show real message history
   */
  public syncMatchWithConversation(match: Match): {
    lastMessage: string;
    lastMessageTime: string;
    unreadCount: number;
    hasConversation: boolean;
  } {
    const { getLastMessageForUser, getUnreadCountForUser, findConversationByParticipant } = useMessagesStore.getState();
    
    const otherUserId = match.users.find(id => id !== 'current-user');
    if (!otherUserId) {
      return {
        lastMessage: 'Say hello to your new match! 👋',
        lastMessageTime: this.formatTime(match.timestamp),
        unreadCount: 0,
        hasConversation: false,
      };
    }

    const conversation = findConversationByParticipant(otherUserId);
    const lastMessage = getLastMessageForUser(otherUserId);
    const unreadCount = getUnreadCountForUser(otherUserId);

    return {
      lastMessage: lastMessage?.content || 'Say hello to your new match! 👋',
      lastMessageTime: lastMessage ? this.formatTime(lastMessage.timestamp) : this.formatTime(match.timestamp),
      unreadCount,
      hasConversation: !!conversation,
    };
  }

  /**
   * Create or find conversation for a match
   */
  public async createConversationForMatch(match: Match): Promise<string> {
    const { 
      createConversation, 
      setCurrentUser, 
      findConversationByParticipant 
    } = useMessagesStore.getState();
    
    const otherUserId = match.users.find(id => id !== 'current-user');
    if (!otherUserId) {
      throw new Error('Invalid match: no other user found');
    }

    // Ensure current user is set
    setCurrentUser({
      id: 'current-user',
      name: 'You',
      avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      isOnline: true,
    });

    // Check if conversation already exists
    const existingConversation = findConversationByParticipant(otherUserId);
    if (existingConversation) {
      return existingConversation.id;
    }

    // Create new conversation
    const otherUser = {
      id: otherUserId,
      name: this.getUserName(otherUserId),
      avatar: this.getUserPhoto(otherUserId),
      isOnline: Math.random() > 0.5, // Random online status for demo
    };

    const conversationId = await createConversation([
      {
        id: 'current-user',
        name: 'You',
        avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: true,
      },
      otherUser
    ]);

    return conversationId;
  }

  /**
   * Get all matches with synchronized conversation data
   */
  public getMatchesWithConversationData(): Array<Match & {
    conversationData: {
      lastMessage: string;
      lastMessageTime: string;
      unreadCount: number;
      hasConversation: boolean;
    };
  }> {
    const { matches } = useLikesStore.getState();
    
    return matches.map(match => ({
      ...match,
      conversationData: this.syncMatchWithConversation(match),
    }));
  }

  /**
   * Create conversation for a liked user (from Likes section)
   */
  public async createConversationForLike(userId: string): Promise<string> {
    const { createConversation, findConversationByParticipant, setCurrentUser } = useMessagesStore.getState();

    // Ensure current user is set
    setCurrentUser({
      id: 'current-user',
      name: 'You',
      avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      isOnline: true,
    });

    // Check if conversation already exists
    const existingConversation = findConversationByParticipant(userId);
    if (existingConversation) {
      return existingConversation.id;
    }

    // Create new conversation
    const otherUser = {
      id: userId,
      name: this.getUserName(userId),
      avatar: this.getUserPhoto(userId),
      isOnline: Math.random() > 0.5, // Random online status for demo
    };

    const conversationId = await createConversation([
      {
        id: 'current-user',
        name: 'You',
        avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: true,
      },
      otherUser
    ]);

    return conversationId;
  }

  /**
   * Update match when new message is sent
   */
  public updateMatchOnNewMessage(userId: string, messageContent: string): void {
    // This would typically update the match's lastActivity timestamp
    // For now, we rely on the real-time sync in syncMatchWithConversation
    console.log(`Match updated for user ${userId} with new message: ${messageContent}`);
  }

  /**
   * Format timestamp for display
   */
  private formatTime(timestamp: Date | string): string {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  }

  /**
   * Get user name by ID
   */
  private getUserName(userId: string): string {
    const userNames: { [key: string]: string } = {
      'user_1': 'Sophia',
      'user_2': 'Emma',
      'user_3': 'Isabella',
      'user_4': 'Olivia',
      'user_5': 'Ava',
      'user_6': 'Mia',
      'user_7': 'Charlotte',
      'user_8': 'Amelia',
    };
    return userNames[userId] || 'Match User';
  }

  /**
   * Get user photo by ID
   */
  private getUserPhoto(userId: string): string {
    const userPhotos: { [key: string]: string } = {
      'user_1': 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_2': 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_3': 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_4': 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_5': 'https://images.pexels.com/photos/1858175/pexels-photo-1858175.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_6': 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_7': 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_8': 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    };
    return userPhotos[userId] || 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400';
  }

  /**
   * Initialize conversations for all existing matches
   * This should be called when the app starts
   */
  public async initializeMatchConversations(): Promise<void> {
    const { matches } = useLikesStore.getState();
    
    for (const match of matches) {
      try {
        await this.createConversationForMatch(match);
      } catch (error) {
        console.warn(`Failed to initialize conversation for match ${match.id}:`, error);
      }
    }
  }

  /**
   * Clean up conversations that no longer have matches
   */
  public cleanupOrphanedConversations(): void {
    const { matches } = useLikesStore.getState();
    const { conversations } = useMessagesStore.getState();
    
    const matchUserIds = new Set(
      matches.flatMap(match => match.users.filter(id => id !== 'current-user'))
    );

    const orphanedConversations = conversations.filter(conv => {
      const otherUser = conv.participants.find(p => p.id !== 'current-user');
      return otherUser && !matchUserIds.has(otherUser.id);
    });

    if (orphanedConversations.length > 0) {
      console.log(`Found ${orphanedConversations.length} orphaned conversations`);
      // In a real app, you might want to archive or delete these
    }
  }
}

// Export singleton instance
export const matchesMessagesIntegration = MatchesMessagesIntegration.getInstance();
