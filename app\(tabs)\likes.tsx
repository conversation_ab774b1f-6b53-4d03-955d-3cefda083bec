import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, MessageCircle, Crown, AlertCircle, Wifi, WifiOff } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { useLikesStore } from '@/stores/likesStore';
import { useMessagesStore } from '@/stores/messagesStore';
import { usePremiumStore } from '@/stores/premiumStore';
import { matchesMessagesIntegration } from '@/services/matchesMessagesIntegration';
import AnimatedLikeCard from '@/components/likes/AnimatedLikeCard';
import AnimatedMatchCard from '@/components/likes/AnimatedMatchCard';
import { LikesGridSkeleton, MatchesListSkeleton } from '@/components/ui/LikesSkeleton';
import { useRealTimeLikes } from '@/hooks/useRealTimeLikes';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

export default function LikesTab() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'likes' | 'matches'>('likes');

  const {
    receivedLikes,
    matches,
    isLoading,
    isRefreshing,
    error,
    isOnline,
    hasMoreLikes,
    hasMoreMatches,
    fetchReceivedLikes,
    fetchMatches,
    likeBack,
    clearError,
    canViewWhoLikesYou,
    getPremiumStatus,
  } = useLikesStore();

  // Premium store integration
  const {
    isPremium,
    hasFeature,
    loadSubscription,
  } = usePremiumStore();

  // Get comprehensive premium status
  const premiumStatus = getPremiumStatus();

  // Messages store integration
  const {
    createConversation,
    findConversationByParticipant,
  } = useMessagesStore();

  // Initialize real-time likes functionality
  const { isConnected } = useRealTimeLikes();

  // Haptic feedback helper
  const triggerHaptic = {
    light: () => {
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    },
    medium: () => {
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    },
  };

  useEffect(() => {
    let isMounted = true;

    // Initial data fetch
    const initializeData = async () => {
      try {
        // Load premium subscription status
        await loadSubscription();

        if (isMounted && receivedLikes.length === 0) {
          await fetchReceivedLikes();
        }
        if (isMounted && matches.length === 0) {
          await fetchMatches();
        }

        // Initialize match-message integration
        if (isMounted) {
          await matchesMessagesIntegration.initializeMatchConversations();
        }
      } catch (error) {
        console.error('Error initializing likes data:', error);
      }
    };

    initializeData();

    return () => {
      isMounted = false;
    };
  }, [fetchReceivedLikes, fetchMatches, receivedLikes.length, matches.length, loadSubscription]);

  const handleRefresh = async () => {
    if (activeTab === 'likes') {
      await fetchReceivedLikes(true);
    } else {
      await fetchMatches(true);
    }
  };

  const handleLoadMore = () => {
    if (activeTab === 'likes' && hasMoreLikes && !isLoading) {
      fetchReceivedLikes();
    } else if (activeTab === 'matches' && hasMoreMatches && !isLoading) {
      fetchMatches();
    }
  };

  const handleLikeBack = async (userId: string) => {
    try {
      const isMatch = await likeBack(userId);
      if (isMatch) {
        // Switch to matches tab to show the new match
        setTimeout(() => setActiveTab('matches'), 2000);
      }
      return isMatch;
    } catch (error) {
      Alert.alert('Error', 'Failed to like back. Please try again.');
      return false;
    }
  };

  const handleMessage = async (userId: string) => {
    try {
      triggerHaptic.medium();

      console.log('Starting conversation with user:', userId);

      // Use integration service to create or find conversation
      const conversationId = await matchesMessagesIntegration.createConversationForLike(userId);

      console.log('Conversation created/found:', conversationId);

      // Small delay to ensure conversation is properly created
      setTimeout(() => {
        console.log('Navigating to chat with user:', userId);
        router.push(`/chat/${userId}`);
      }, 100);

    } catch (error) {
      console.error('Error starting conversation:', error);
      Alert.alert(
        'Error',
        `Unable to start conversation with user ${userId}. Please try again.\n\nError: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  };

  const handleMatchPress = async (matchId: string) => {
    try {
      triggerHaptic.medium();

      // Find the match and navigate to chat
      const match = matches.find(m => m.id === matchId);
      if (match) {
        const otherUserId = match.users.find(id => id !== 'current-user');
        if (otherUserId) {
          // Use integration service to create or find conversation
          await matchesMessagesIntegration.createConversationForMatch(match);

          // Navigate to messages tab and then to specific chat
          router.push('/(tabs)/messages');

          // Small delay to ensure navigation completes
          setTimeout(() => {
            router.push(`/chat/${otherUserId}`);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error creating conversation for match:', error);
      Alert.alert('Error', 'Unable to open conversation. Please try again.');
    }
  };

  const handleProfilePress = (profile: any) => {
    triggerHaptic.light();
    // Navigate to profile detail view
    console.log('View profile:', profile.id);
    // TODO: Implement profile detail navigation
  };

  const handleUpgradePress = () => {
    triggerHaptic.medium();
    router.push('/premium');
  };

  // Helper functions to get match user data
  const getMatchUserName = (userId: string): string => {
    const userNames: { [key: string]: string } = {
      'user_1': 'Sophia',
      'user_2': 'Emma',
      'user_3': 'Isabella',
      'user_4': 'Olivia',
      'user_5': 'Ava',
      'user_6': 'Mia',
      'user_7': 'Charlotte',
      'user_8': 'Amelia',
    };
    return userNames[userId] || 'Match User';
  };

  const getMatchUserAge = (userId: string): number => {
    const userAges: { [key: string]: number } = {
      'user_1': 26,
      'user_2': 24,
      'user_3': 28,
      'user_4': 25,
      'user_5': 27,
      'user_6': 23,
      'user_7': 29,
      'user_8': 26,
    };
    return userAges[userId] || 25;
  };

  const getMatchUserPhoto = (userId: string): string => {
    const userPhotos: { [key: string]: string } = {
      'user_1': 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_2': 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_3': 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_4': 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_5': 'https://images.pexels.com/photos/1858175/pexels-photo-1858175.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_6': 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_7': 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=400',
      'user_8': 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    };
    return userPhotos[userId] || 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400';
  };

  const getLastMessage = (matchId: string, otherUserId: string): string => {
    const { getLastMessageForUser } = useMessagesStore.getState();
    const lastMessage = getLastMessageForUser(otherUserId);

    if (lastMessage) {
      return lastMessage.content;
    }

    // Fallback to static messages for demo
    const lastMessages: { [key: string]: string } = {
      'match_1': 'Hey! Thanks for the like 😊',
      'match_2': 'Would love to grab coffee sometime!',
      'match_3': 'That hiking photo is amazing! 🏔️',
      'match_4': 'Your profile made me smile 😄',
      'match_5': 'We have so much in common!',
      'match_6': 'Looking forward to chatting more',
      'match_7': 'Great taste in music! 🎵',
      'match_8': 'Hope you\'re having a great day!',
    };
    return lastMessages[matchId] || 'Say hello to your new match! 👋';
  };

  const hasUnreadMessages = (matchId: string, otherUserId: string): boolean => {
    const { getUnreadCountForUser } = useMessagesStore.getState();
    const unreadCount = getUnreadCountForUser(otherUserId);

    if (unreadCount > 0) {
      return true;
    }

    // Fallback for demo
    const unreadMatches = ['match_1', 'match_3', 'match_5'];
    return unreadMatches.includes(matchId);
  };

  const getLastMessageTime = (matchId: string, otherUserId: string): string => {
    const { getLastMessageForUser } = useMessagesStore.getState();
    const lastMessage = getLastMessageForUser(otherUserId);

    if (lastMessage) {
      const now = new Date();
      const messageTime = new Date(lastMessage.timestamp);
      const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }

    // Fallback to match timestamp
    const match = matches.find(m => m.id === matchId);
    if (match) {
      const now = new Date();
      const matchTime = match.lastActivity || match.timestamp;
      const diffInMinutes = Math.floor((now.getTime() - matchTime.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }

    return 'Recently';
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <View style={styles.errorContainer}>
        <AlertCircle size={24} color={theme.colors.error} />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={clearError}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (activeTab === 'likes') {
      return (
        <View style={styles.emptyState}>
          <Heart size={64} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.emptyTitle}>No likes yet</Text>
          <Text style={styles.emptySubtitle}>
            Keep swiping to find people who like you!
          </Text>
        </View>
      );
    } else {
      return (
        <View style={styles.emptyState}>
          <MessageCircle size={64} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.emptyTitle}>No matches yet</Text>
          <Text style={styles.emptySubtitle}>
            Start swiping to find your perfect match!
          </Text>
        </View>
      );
    }
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Text style={styles.title}>Likes & Matches</Text>
          {!isOnline && (
            <View style={styles.offlineIndicator}>
              <WifiOff size={16} color="white" />
              <Text style={styles.offlineText}>Offline</Text>
            </View>
          )}
        </View>

        <View style={styles.tabBar}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'likes' && styles.activeTab]}
            onPress={() => setActiveTab('likes')}
          >
            <Heart
              size={20}
              color={activeTab === 'likes' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'likes' && styles.activeTabText,
              ]}
            >
              Likes ({receivedLikes.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'matches' && styles.activeTab]}
            onPress={() => setActiveTab('matches')}
          >
            <MessageCircle
              size={20}
              color={activeTab === 'matches' ? '#8B5CF6' : 'rgba(255, 255, 255, 0.7)'}
            />
            <Text
              style={[
                styles.tabText,
                activeTab === 'matches' && styles.activeTabText,
              ]}
            >
              Matches ({matches.length})
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="white"
              colors={['white']}
            />
          }
          onMomentumScrollEnd={(event) => {
            const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
            const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
            if (isCloseToBottom) {
              handleLoadMore();
            }
          }}
        >
          {error && renderError()}

          {activeTab === 'likes' ? (
            <View style={styles.likesContainer}>
              {/* Premium Banner - Show only if user doesn't have premium */}
              {!premiumStatus.canViewLikes && (
                <View style={styles.premiumBanner}>
                  <Crown size={24} color="#FFD700" />
                  <View style={styles.premiumBannerContent}>
                    <Text style={styles.premiumBannerTitle}>See Who Likes You</Text>
                    <Text style={styles.premiumBannerSubtitle}>
                      Upgrade to Premium to see all your likes instantly
                    </Text>
                  </View>
                  <TouchableOpacity style={styles.upgradeButton} onPress={handleUpgradePress}>
                    <Text style={styles.upgradeButtonText}>Upgrade</Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Premium Status Banner - Show if user has premium */}
              {premiumStatus.canViewLikes && (
                <View style={styles.premiumStatusBanner}>
                  <Crown size={20} color="#FFD700" />
                  <Text style={styles.premiumStatusText}>
                    Premium Active - See all your likes
                  </Text>
                </View>
              )}

              <View style={styles.likesGrid}>
                {receivedLikes.map((profile) => (
                  <AnimatedLikeCard
                    key={profile.id}
                    profile={profile}
                    onLikeBack={handleLikeBack}
                    onMessage={handleMessage}
                    onPress={handleProfilePress}
                  />
                ))}

                {isLoading && !isRefreshing && (
                  <LikesGridSkeleton count={4} />
                )}
              </View>

              {receivedLikes.length === 0 && !isLoading && renderEmptyState()}
            </View>
          ) : (
            <View style={styles.matchesContainer}>
              {matches.map((match) => {
                // Get the other user ID (not current user)
                const otherUserId = match.users.find(id => id !== 'current-user') || match.users[0];

                // Use integration service to get synchronized conversation data
                const conversationData = matchesMessagesIntegration.syncMatchWithConversation(match);

                // Create enhanced match data with real conversation information
                const matchData = {
                  id: match.id,
                  otherUser: {
                    id: otherUserId,
                    name: getMatchUserName(otherUserId),
                    age: getMatchUserAge(otherUserId),
                    photo: getMatchUserPhoto(otherUserId),
                    lastMessage: conversationData.lastMessage,
                    lastMessageTime: conversationData.lastMessageTime,
                    unread: conversationData.unreadCount > 0,
                  },
                  timestamp: match.timestamp,
                  status: match.status,
                };

                return (
                  <AnimatedMatchCard
                    key={match.id}
                    match={matchData}
                    onPress={handleMatchPress}
                  />
                );
              })}

              {isLoading && !isRefreshing && (
                <MatchesListSkeleton count={3} />
              )}

              {matches.length === 0 && !isLoading && renderEmptyState()}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  offlineText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: 24,
    borderRadius: 12,
    padding: 4,
    marginBottom: 24,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  activeTab: {
    backgroundColor: 'white',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  activeTabText: {
    color: '#8B5CF6',
  },
  content: {
    flex: 1,
  },
  likesContainer: {
    paddingHorizontal: 24,
  },
  premiumBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  premiumBannerContent: {
    flex: 1,
    marginLeft: 12,
  },
  premiumBannerTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  premiumBannerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  upgradeButton: {
    backgroundColor: '#FFD700',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000',
  },
  likesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    paddingBottom: 24,
  },
  likeCard: {
    width: CARD_WIDTH,
    height: CARD_WIDTH * 1.3,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  likeCardImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  premiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    padding: 6,
    zIndex: 1,
  },
  likeCardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
  },
  likeCardInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 12,
  },
  likeCardName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 8,
  },
  matchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#42DDA6',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  matchButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  likeBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
  },
  likeBackButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#42DDA6',
  },
  matchesContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  matchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    position: 'relative',
  },
  matchPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  matchInfo: {
    flex: 1,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  matchName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  matchTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.6)',
  },
  matchMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  unreadMessage: {
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#42DDA6',
    position: 'absolute',
    right: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: 'white',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.error,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  premiumStatusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 24,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
  },
  premiumStatusText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFD700',
    marginLeft: 8,
  },
});